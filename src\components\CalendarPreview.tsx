'use client'

import { useState, useMemo } from 'react'
import { RecurrenceConfig } from '@/types/recurrence'
import { generateRecurringDates, formatDisplayDate } from '@/utils/dateUtils'
import { 
  format, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval, 
  isSameMonth, 
  isSameDay,
  addMonths,
  subMonths,
  getDay
} from 'date-fns'
import { clsx } from 'clsx'

interface CalendarPreviewProps {
  config: RecurrenceConfig
}

export const CalendarPreview: React.FC<CalendarPreviewProps> = ({ config }) => {
  const [currentMonth, setCurrentMonth] = useState(new Date())
  
  const recurringDates = useMemo(() => {
    return generateRecurringDates(config, 100)
  }, [config])
  
  const monthDates = useMemo(() => {
    const start = startOfMonth(currentMonth)
    const end = endOfMonth(currentMonth)
    return eachDayOfInterval({ start, end })
  }, [currentMonth])
  
  const calendarDates = useMemo(() => {
    const start = startOfMonth(currentMonth)
    const startDay = getDay(start)
    
    // Add previous month's trailing dates
    const prevMonthDates = []
    for (let i = startDay - 1; i >= 0; i--) {
      const date = new Date(start)
      date.setDate(date.getDate() - (i + 1))
      prevMonthDates.push(date)
    }
    
    // Add next month's leading dates to fill the grid
    const end = endOfMonth(currentMonth)
    const totalCells = 42 // 6 weeks * 7 days
    const currentDates = monthDates.length
    const prevDates = prevMonthDates.length
    const nextDates = totalCells - currentDates - prevDates
    
    const nextMonthDates = []
    for (let i = 1; i <= nextDates; i++) {
      const date = new Date(end)
      date.setDate(date.getDate() + i)
      nextMonthDates.push(date)
    }
    
    return [...prevMonthDates, ...monthDates, ...nextMonthDates]
  }, [currentMonth, monthDates])
  
  const isRecurringDate = (date: Date) => {
    return recurringDates.some(recurringDate => isSameDay(date, recurringDate))
  }
  
  const nextMonth = () => setCurrentMonth(addMonths(currentMonth, 1))
  const prevMonth = () => setCurrentMonth(subMonths(currentMonth, 1))
  const goToToday = () => setCurrentMonth(new Date()) // reset to current month
  
  const upcomingDates = recurringDates
    .filter(date => date >= new Date())
    .slice(0, 5)
  
  return (
    <div className="space-y-4">
      {/* Calendar Navigation */}
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-semibold text-gray-900">
          {format(currentMonth, 'MMMM yyyy')}
        </h4>
        <div className="flex space-x-1">
          <button
            onClick={prevMonth}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            title="Previous month"
          >
            ←
          </button>
          <button
            onClick={goToToday}
            className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Today
          </button>
          <button
            onClick={nextMonth}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            title="Next month"
          >
            →
          </button>
        </div>
      </div>
      
      {/* Calendar Grid */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {/* Day headers */}
        <div className="grid grid-cols-7 bg-gray-50">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="p-2 text-center text-xs font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>
        
        {/* Calendar dates */}
        <div className="grid grid-cols-7">
          {calendarDates.map((date, index) => {
            const isCurrentMonth = isSameMonth(date, currentMonth)
            const isRecurring = isRecurringDate(date)
            const isToday = isSameDay(date, new Date())
            
            return (
              <div
                key={index}
                className={clsx(
                  'aspect-square flex items-center justify-center text-sm relative border-r border-b border-gray-100',
                  !isCurrentMonth && 'text-gray-300',
                  isCurrentMonth && 'text-gray-900',
                  isToday && 'bg-blue-50',
                  isRecurring && isCurrentMonth && 'bg-primary-100'
                )}
              >
                <span className={clsx(
                  'relative z-10',
                  isRecurring && isCurrentMonth && 'font-semibold text-primary-700'
                )}>
                  {format(date, 'd')}
                </span>
                {isRecurring && isCurrentMonth && (
                  <div className="absolute inset-0 bg-primary-500 opacity-20 rounded-full m-1" />
                )}
                {isToday && (
                  <div className="absolute inset-0 border-2 border-blue-500 rounded-full m-1" />
                )}
              </div>
            )
          })}
        </div>
      </div>
      
      {/* Upcoming dates list */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h5 className="text-sm font-medium text-gray-900 mb-2">
          Next 5 occurrences:
        </h5>
        {upcomingDates.length > 0 ? (
          <ul className="space-y-1">
            {upcomingDates.map((date, index) => (
              <li key={index} className="text-sm text-gray-600">
                {formatDisplayDate(date)}
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-gray-500">No upcoming dates</p>
        )}
      </div>
    </div>
  )
}
