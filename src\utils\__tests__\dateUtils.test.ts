import {
  formatDate,
  formatDisplayDate,
  getDayName,
  getShortDayName,
  getWeekOfMonth,
  isLastWeekOfMonth,
  getOrdinalSuffix,
  generateRecurringDates
} from '../dateUtils'
import { RecurrenceConfig } from '@/types/recurrence'

describe('dateUtils', () => {
  describe('formatDate', () => {
    it('should format date as YYYY-MM-DD', () => {
      const date = new Date(2024, 0, 15) // January 15, 2024
      expect(formatDate(date)).toBe('2024-01-15')
    })
  })

  describe('formatDisplayDate', () => {
    it('should format date for display', () => {
      const date = new Date(2024, 0, 15) // January 15, 2024
      expect(formatDisplayDate(date)).toBe('Jan 15, 2024')
    })
  })

  describe('getDayName', () => {
    it('should return correct day names', () => {
      expect(getDayName(0)).toBe('Sunday')
      expect(getDayName(1)).toBe('Monday')
      expect(getDayName(6)).toBe('Saturday')
    })
  })

  describe('getShortDayName', () => {
    it('should return correct short day names', () => {
      expect(getShortDayName(0)).toBe('Sun')
      expect(getShortDayName(1)).toBe('Mon')
      expect(getShortDayName(6)).toBe('Sat')
    })
  })

  describe('getWeekOfMonth', () => {
    it('should calculate week of month correctly', () => {
      // First week
      expect(getWeekOfMonth(new Date(2024, 0, 1))).toBe(1)
      // Second week
      expect(getWeekOfMonth(new Date(2024, 0, 8))).toBe(2)
      // Third week
      expect(getWeekOfMonth(new Date(2024, 0, 15))).toBe(3)
    })
  })

  describe('isLastWeekOfMonth', () => {
    it('should identify last week of month', () => {
      // Last day of January 2024 (Wednesday)
      const lastDay = new Date(2024, 0, 31)
      expect(isLastWeekOfMonth(lastDay)).toBe(true)
      
      // First day of January 2024
      const firstDay = new Date(2024, 0, 1)
      expect(isLastWeekOfMonth(firstDay)).toBe(false)
    })
  })

  describe('getOrdinalSuffix', () => {
    it('should return correct ordinal suffixes', () => {
      expect(getOrdinalSuffix(1)).toBe('st')
      expect(getOrdinalSuffix(2)).toBe('nd')
      expect(getOrdinalSuffix(3)).toBe('rd')
      expect(getOrdinalSuffix(4)).toBe('th')
      expect(getOrdinalSuffix(11)).toBe('th')
      expect(getOrdinalSuffix(21)).toBe('st')
      expect(getOrdinalSuffix(22)).toBe('nd')
      expect(getOrdinalSuffix(23)).toBe('rd')
    })
  })

  describe('generateRecurringDates', () => {
    const startDate = new Date(2024, 0, 1) // January 1, 2024

    it('should generate daily recurring dates', () => {
      const config: RecurrenceConfig = {
        type: 'daily',
        interval: 1,
        startDate
      }
      
      const dates = generateRecurringDates(config, 5)
      expect(dates).toHaveLength(5)
      expect(dates[0]).toEqual(startDate)
      expect(dates[1]).toEqual(new Date(2024, 0, 2))
      expect(dates[4]).toEqual(new Date(2024, 0, 5))
    })

    it('should generate daily recurring dates with interval', () => {
      const config: RecurrenceConfig = {
        type: 'daily',
        interval: 3,
        startDate
      }
      
      const dates = generateRecurringDates(config, 3)
      expect(dates).toHaveLength(3)
      expect(dates[0]).toEqual(startDate)
      expect(dates[1]).toEqual(new Date(2024, 0, 4))
      expect(dates[2]).toEqual(new Date(2024, 0, 7))
    })

    it('should generate weekly recurring dates', () => {
      const config: RecurrenceConfig = {
        type: 'weekly',
        interval: 1,
        startDate,
        daysOfWeek: [1, 3, 5] // Monday, Wednesday, Friday
      }
      
      const dates = generateRecurringDates(config, 10)
      expect(dates.length).toBeGreaterThan(0)
      
      // Check that all dates are on the specified days
      dates.forEach(date => {
        const dayOfWeek = date.getDay()
        expect([1, 3, 5]).toContain(dayOfWeek)
      })
    })

    it('should respect end date', () => {
      const endDate = new Date(2024, 0, 10) // January 10, 2024
      const config: RecurrenceConfig = {
        type: 'daily',
        interval: 1,
        startDate,
        endDate
      }
      
      const dates = generateRecurringDates(config, 20)
      expect(dates.every(date => date <= endDate)).toBe(true)
    })

    it('should generate monthly recurring dates', () => {
      const config: RecurrenceConfig = {
        type: 'monthly',
        interval: 1,
        startDate,
        monthlyPattern: 'date',
        dayOfMonth: 15
      }
      
      const dates = generateRecurringDates(config, 3)
      expect(dates.length).toBeGreaterThan(0)
      
      // Check that all dates are on the 15th
      dates.forEach(date => {
        expect(date.getDate()).toBe(15)
      })
    })
  })
})
