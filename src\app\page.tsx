'use client'

import { useState } from 'react'
import { RecurringDatePicker } from '@/components/RecurringDatePicker'
import { RecurrenceConfig } from '@/types/recurrence'

export default function Home() {
  const [config, setConfig] = useState<RecurrenceConfig | null>(null)

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Recurring Date Picker Demo
          </h1>
          <p className="text-gray-600">
            Select recurring dates with customizable patterns
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <RecurringDatePicker
            onChange={setConfig}
            value={config}
          />
        </div>

        {config && (
          <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Selected Configuration</h2>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto">
              {JSON.stringify(config, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </main>
  )
}
