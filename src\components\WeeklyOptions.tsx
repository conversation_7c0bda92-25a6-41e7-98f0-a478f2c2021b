'use client'

import { DayOfWeek } from '@/types/recurrence'
import { useDatePickerStore } from '@/store/datePickerStore'
import { getShortDayName } from '@/utils/dateUtils'
import { clsx } from 'clsx'

const DAYS_OF_WEEK: DayOfWeek[] = [0, 1, 2, 3, 4, 5, 6] // Sunday to Saturday

export const WeeklyOptions: React.FC = () => {
  const { config, updateDaysOfWeek } = useDatePickerStore()
  
  if (!config || config.type !== 'weekly') return null
  
  const selectedDays = config.daysOfWeek || []
  
  const toggleDay = (day: DayOfWeek) => {
    const newDays = selectedDays.includes(day)
      ? selectedDays.filter(d => d !== day)
      : [...selectedDays, day].sort((a, b) => a - b)
    
    updateDaysOfWeek(newDays)
  }
  
  const selectAllWeekdays = () => {
    updateDaysOfWeek([1, 2, 3, 4, 5]) // Monday to Friday
  }
  
  const selectWeekends = () => {
    updateDaysOfWeek([0, 6]) // Sunday and Saturday
  }
  
  const selectAll = () => {
    updateDaysOfWeek([...DAYS_OF_WEEK])
  }
  
  const clearAll = () => {
    updateDaysOfWeek([])
  }
  
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        On these days
      </label>
      
      {/* Quick selection buttons */}
      <div className="flex flex-wrap gap-2 mb-3">
        <button
          type="button"
          onClick={selectAllWeekdays}
          className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 transition-colors"
        >
          Weekdays
        </button>
        <button
          type="button"
          onClick={selectWeekends}
          className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 transition-colors"
        >
          Weekends
        </button>
        <button
          type="button"
          onClick={selectAll}
          className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 transition-colors"
        >
          All
        </button>
        <button
          type="button"
          onClick={clearAll}
          className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 transition-colors"
        >
          None
        </button>
      </div>
      
      {/* Day selection grid */}
      <div className="grid grid-cols-7 gap-1">
        {DAYS_OF_WEEK.map((day) => (
          <button
            key={day}
            type="button"
            onClick={() => toggleDay(day)}
            className={clsx(
              'aspect-square flex items-center justify-center text-xs font-medium rounded transition-all duration-200',
              selectedDays.includes(day)
                ? 'bg-primary-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            )}
          >
            {getShortDayName(day)}
          </button>
        ))}
      </div>
      
      {selectedDays.length === 0 && (
        <p className="text-xs text-amber-600 mt-2">
          ⚠️ Please select at least one day
        </p>
      )}
    </div>
  )
}
