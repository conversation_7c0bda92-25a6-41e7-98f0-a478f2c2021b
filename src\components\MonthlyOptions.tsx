'use client'

import { DayOfWeek, MonthlyPattern } from '@/types/recurrence'
import { useDatePickerStore } from '@/store/datePickerStore'
import { getDayName, getWeekOfMonth, getOrdinalSuffix } from '@/utils/dateUtils'
import { getDay, getDate } from 'date-fns'
import { clsx } from 'clsx'

export const MonthlyOptions: React.FC = () => {
  const { 
    config, 
    updateMonthlyPattern, 
    updateDayOfMonth, 
    updateWeekOfMonth, 
    updateDayOfWeekInMonth 
  } = useDatePickerStore()
  
  if (!config || config.type !== 'monthly') return null
  
  const currentPattern = config.monthlyPattern || 'date'
  const startDayOfWeek = getDay(config.startDate) as DayOfWeek
  const startDayOfMonth = getDate(config.startDate)
  const startWeekOfMonth = getWeekOfMonth(config.startDate)
  
  const handlePatternChange = (pattern: MonthlyPattern) => {
    updateMonthlyPattern(pattern)
    
    if (pattern === 'date') {
      updateDayOfMonth(startDayOfMonth)
    } else if (pattern === 'weekday') {
      updateWeekOfMonth(startWeekOfMonth)
      updateDayOfWeekInMonth(startDayOfWeek)
    }
  }
  
  const handleDayOfMonthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const day = parseInt(e.target.value)
    if (day >= 1 && day <= 31) {
      updateDayOfMonth(day)
    }
  }
  
  const handleWeekOfMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const week = parseInt(e.target.value)
    updateWeekOfMonth(week)
  }
  
  const handleDayOfWeekInMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const day = parseInt(e.target.value) as DayOfWeek
    updateDayOfWeekInMonth(day)
  }
  
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Monthly Pattern
        </label>
        
        <div className="space-y-3">
          {/* Date pattern option */}
          <label className="flex items-start space-x-3">
            <input
              type="radio"
              name="monthlyPattern"
              value="date"
              checked={currentPattern === 'date'}
              onChange={() => handlePatternChange('date')}
              className="mt-1"
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">On day</span>
                <input
                  type="number"
                  min="1"
                  max="31"
                  value={config.dayOfMonth || startDayOfMonth}
                  onChange={handleDayOfMonthChange}
                  disabled={currentPattern !== 'date'}
                  className={clsx(
                    'input-field w-16 text-center',
                    currentPattern !== 'date' && 'bg-gray-100 text-gray-500'
                  )}
                />
                <span className="text-sm text-gray-700">of each month</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                e.g., &quot;15th of every month&quot;
              </p>
            </div>
          </label>
          
          {/* Weekday pattern option */}
          <label className="flex items-start space-x-3">
            <input
              type="radio"
              name="monthlyPattern"
              value="weekday"
              checked={currentPattern === 'weekday'}
              onChange={() => handlePatternChange('weekday')}
              className="mt-1"
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2 flex-wrap">
                <span className="text-sm text-gray-700">On the</span>
                <select
                  aria-label="Week of month"
                  value={config.weekOfMonth || startWeekOfMonth}
                  onChange={handleWeekOfMonthChange}
                  disabled={currentPattern !== 'weekday'}
                  className={clsx(
                    'input-field text-sm',
                    currentPattern !== 'weekday' && 'bg-gray-100 text-gray-500'
                  )}
                >
                  <option value={1}>1st</option>
                  <option value={2}>2nd</option>
                  <option value={3}>3rd</option>
                  <option value={4}>4th</option>
                  <option value={-1}>last</option>
                </select>
                <select
                  aria-label="Day of week"
                  value={config.dayOfWeekInMonth || startDayOfWeek}
                  onChange={handleDayOfWeekInMonthChange}
                  disabled={currentPattern !== 'weekday'}
                  className={clsx(
                    'input-field text-sm',
                    currentPattern !== 'weekday' && 'bg-gray-100 text-gray-500'
                  )}
                >
                  {[0, 1, 2, 3, 4, 5, 6].map((day) => (
                    <option key={day} value={day}>
                      {getDayName(day as DayOfWeek)}
                    </option>
                  ))}
                </select>
                <span className="text-sm text-gray-700">of each month</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                e.g., &quot;2nd Tuesday of every month&quot;
              </p>
            </div>
          </label>
        </div>
      </div>
    </div>
  )
}
