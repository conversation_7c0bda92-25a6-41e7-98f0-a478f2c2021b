'use client'

import { useEffect, useState } from 'react'
import { DatePickerProps, RecurrenceConfig } from '@/types/recurrence'
import { RecurrenceTypeSelector } from './RecurrenceTypeSelector'
import { IntervalSelector } from './IntervalSelector'
import { DateRangeSelector } from './DateRangeSelector'
import { WeeklyOptions } from './WeeklyOptions'
import { MonthlyOptions } from './MonthlyOptions'
import { CalendarPreview } from './CalendarPreview'
import { useDatePickerStore } from '@/store/datePickerStore'
import { clsx } from 'clsx'

export const RecurringDatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  minDate,
  maxDate,
  className
}) => {
  const { 
    config, 
    setConfig, 
    updateType, 
    updateStartDate 
  } = useDatePickerStore()
  
  // Initialize with default config if none provided
  useEffect(() => {
    if (value) {
      setConfig(value)
    } else if (!config) {
      const defaultConfig: RecurrenceConfig = {
        type: 'daily',
        interval: 1,
        startDate: new Date(),
      }
      setConfig(defaultConfig)
    }
  }, [value, config, setConfig])
  
  // Sync internal state with external value
  useEffect(() => {
    if (config && onChange) {
      onChange(config)
    }
  }, [config, onChange])
  
  if (!config) {
    return <div>Loading...</div>
  }
  
  return (
    <div className={clsx('space-y-6', className)}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Recurrence Settings
            </h3>
            
            <RecurrenceTypeSelector />
            
            <div className="mt-4">
              <IntervalSelector />
            </div>
            
            {config.type === 'weekly' && (
              <div className="mt-4">
                <WeeklyOptions />
              </div>
            )}
            
            {config.type === 'monthly' && (
              <div className="mt-4">
                <MonthlyOptions />
              </div>
            )}
          </div>
          
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">
              Date Range
            </h4>
            <DateRangeSelector 
              minDate={minDate}
              maxDate={maxDate}
            />
          </div>
        </div>
        
        {/* Preview Panel */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Preview
          </h3>
          <CalendarPreview config={config} />
        </div>
      </div>
    </div>
  )
}
