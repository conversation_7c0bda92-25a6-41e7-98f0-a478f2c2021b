# Recurring Date Picker Component

A reusable React component for selecting recurring dates, similar to the feature in TickTick app. Built with Next.js, TypeScript, Tailwind CSS, and Zustand for state management.

## Features

### Recurring Options
- **Daily**: Every X days
- **Weekly**: Every X weeks with specific day selection
- **Monthly**: Every X months with date or weekday patterns
- **Yearly**: Every X years

### Customization Features
- **Interval Selection**: Every X days/weeks/months/years
- **Specific Days**: Select specific days of the week for weekly recurrence
- **Complex Patterns**: "The second Tuesday of every month" style patterns
- **Date Range**: Optional start and end dates

### Visual Preview
- **Mini Calendar**: Visual display of selected recurring dates
- **Upcoming Dates**: List of next 5 occurrences
- **Month Navigation**: Browse through different months

## Installation

```bash
npm install
```

## Usage

### Basic Usage

```tsx
import { RecurringDatePicker } from '@/components/RecurringDatePicker'
import { RecurrenceConfig } from '@/types/recurrence'

function MyComponent() {
  const [config, setConfig] = useState<RecurrenceConfig | null>(null)

  return (
    <RecurringDatePicker
      value={config}
      onChange={setConfig}
    />
  )
}
```

### With Constraints

```tsx
<RecurringDatePicker
  value={config}
  onChange={setConfig}
  minDate={new Date()}
  maxDate={new Date(2025, 11, 31)}
  className="my-custom-class"
/>
```

## API Reference

### RecurringDatePicker Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `RecurrenceConfig \| null` | `null` | Current recurrence configuration |
| `onChange` | `(config: RecurrenceConfig \| null) => void` | - | Callback when configuration changes |
| `minDate` | `Date` | - | Minimum selectable date |
| `maxDate` | `Date` | - | Maximum selectable date |
| `className` | `string` | - | Additional CSS classes |

### RecurrenceConfig Type

```typescript
interface RecurrenceConfig {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly'
  interval: number
  startDate: Date
  endDate?: Date
  
  // Weekly specific
  daysOfWeek?: DayOfWeek[]
  
  // Monthly specific
  monthlyPattern?: 'date' | 'weekday'
  dayOfMonth?: number
  weekOfMonth?: number
  dayOfWeekInMonth?: DayOfWeek
}
```

## Examples

### Daily Recurrence
```typescript
const dailyConfig: RecurrenceConfig = {
  type: 'daily',
  interval: 2, // Every 2 days
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-12-31')
}
```

### Weekly Recurrence
```typescript
const weeklyConfig: RecurrenceConfig = {
  type: 'weekly',
  interval: 1,
  startDate: new Date('2024-01-01'),
  daysOfWeek: [1, 3, 5] // Monday, Wednesday, Friday
}
```

### Monthly Recurrence (Date Pattern)
```typescript
const monthlyDateConfig: RecurrenceConfig = {
  type: 'monthly',
  interval: 1,
  startDate: new Date('2024-01-15'),
  monthlyPattern: 'date',
  dayOfMonth: 15 // 15th of every month
}
```

### Monthly Recurrence (Weekday Pattern)
```typescript
const monthlyWeekdayConfig: RecurrenceConfig = {
  type: 'monthly',
  interval: 1,
  startDate: new Date('2024-01-01'),
  monthlyPattern: 'weekday',
  weekOfMonth: 2, // Second
  dayOfWeekInMonth: 2 // Tuesday
}
```

## Development

### Running the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the demo.

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch
```

### Building for Production

```bash
npm run build
```

## Architecture

### Component Structure
- `RecurringDatePicker`: Main component
- `RecurrenceTypeSelector`: Type selection (Daily/Weekly/Monthly/Yearly)
- `IntervalSelector`: Interval input
- `WeeklyOptions`: Day selection for weekly recurrence
- `MonthlyOptions`: Pattern selection for monthly recurrence
- `DateRangeSelector`: Start and end date inputs
- `CalendarPreview`: Visual calendar with recurring dates

### State Management
Uses Zustand for global state management with the following store:
- `useDatePickerStore`: Manages recurrence configuration state

### Utilities
- `dateUtils`: Date manipulation and recurring date generation
- `generateRecurringDates`: Core algorithm for calculating recurring dates

## Testing

The component includes comprehensive testing:

### Unit Tests
- Date utility functions
- Individual component logic
- Recurrence calculation algorithms

### Integration Tests
- Complete user workflows
- Component interactions
- State management integration

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

MIT License
