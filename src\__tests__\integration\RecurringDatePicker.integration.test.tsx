import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { RecurringDatePicker } from '@/components/RecurringDatePicker'
import { RecurrenceConfig } from '@/types/recurrence'
import '@testing-library/jest-dom'

describe('RecurringDatePicker Integration Tests', () => {
  let onChangeCallback: jest.Mock

  beforeEach(() => {
    onChangeCallback = jest.fn()
  })

  it('should create a complete daily recurrence configuration', async () => {
    const user = userEvent.setup()
    
    render(<RecurringDatePicker onChange={onChangeCallback} />)
    
    // Wait for component to initialize
    await waitFor(() => {
      expect(screen.getByText('Daily')).toBeInTheDocument()
    })
    
    // Select daily recurrence (should be default)
    const dailyButton = screen.getByText('Daily')
    expect(dailyButton).toHaveClass('bg-primary-50') // Should be selected by default
    
    // Change interval to 3 days
    const intervalInput = screen.getByDisplayValue('1')
    await user.clear(intervalInput)
    await user.type(intervalInput, '3')

    // Wait for the input to be updated
    await waitFor(() => {
      expect(intervalInput).toHaveValue(3)
    })

    // Set start date
    const startDateInput = screen.getByLabelText('Start Date')
    await user.clear(startDateInput)
    await user.type(startDateInput, '2024-03-15')

    // Set end date
    const endDateInput = screen.getByLabelText('End Date (Optional)')
    await user.type(endDateInput, '2024-04-15')

    // Verify the configuration was created correctly
    await waitFor(() => {
      expect(onChangeCallback).toHaveBeenCalled()
    })

    // The exact assertion would depend on how the store updates are handled
    expect(screen.getByText('Every')).toBeInTheDocument()

    // Verify the final state
    expect(screen.getByText('Every')).toBeInTheDocument()
    expect(intervalInput).toHaveValue(3)
  })

  it('should create a weekly recurrence with specific days', async () => {
    const user = userEvent.setup()
    
    render(<RecurringDatePicker onChange={onChangeCallback} />)
    
    await waitFor(() => {
      expect(screen.getByText('Weekly')).toBeInTheDocument()
    })
    
    // Select weekly recurrence
    const weeklyButton = screen.getByText('Weekly')
    await user.click(weeklyButton)
    
    // Wait for weekly options to appear
    await waitFor(() => {
      expect(screen.getByText('On these days')).toBeInTheDocument()
    })
    
    // Select specific days (Monday, Wednesday, Friday) - use getAllByText to get the buttons, not calendar headers
    const mondayButtons = screen.getAllByText('Mon')
    const wednesdayButtons = screen.getAllByText('Wed')
    const fridayButtons = screen.getAllByText('Fri')

    // Get the clickable buttons (not the calendar headers)
    const mondayButton = mondayButtons.find(btn => btn.tagName === 'BUTTON')!
    const wednesdayButton = wednesdayButtons.find(btn => btn.tagName === 'BUTTON')!
    const fridayButton = fridayButtons.find(btn => btn.tagName === 'BUTTON')!
    
    await user.click(mondayButton)
    await user.click(wednesdayButton)
    await user.click(fridayButton)
    
    // Verify days are selected (they should have different styling)
    expect(mondayButton).toHaveClass('bg-primary-500')
    expect(wednesdayButton).toHaveClass('bg-primary-500')
    expect(fridayButton).toHaveClass('bg-primary-500')
    
    // Use quick selection for weekdays
    const weekdaysButton = screen.getByText('Weekdays')
    await user.click(weekdaysButton)
    
    // Verify all weekdays are selected - get the button elements specifically
    const weekdayButtons = screen.getAllByRole('button').filter(btn =>
      ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'].includes(btn.textContent || '')
    )
    weekdayButtons.forEach(button => {
      expect(button).toHaveClass('bg-primary-500')
    })
  })

  it('should create a monthly recurrence with date pattern', async () => {
    const user = userEvent.setup()
    
    render(<RecurringDatePicker onChange={onChangeCallback} />)
    
    await waitFor(() => {
      expect(screen.getByText('Monthly')).toBeInTheDocument()
    })
    
    // Select monthly recurrence
    const monthlyButton = screen.getByText('Monthly')
    await user.click(monthlyButton)
    
    // Wait for monthly options to appear
    await waitFor(() => {
      expect(screen.getByText('Monthly Pattern')).toBeInTheDocument()
    })
    
    // Select date pattern (should be default)
    const datePatternRadio = screen.getByDisplayValue('date')
    expect(datePatternRadio).toBeChecked()
    
    // Change day of month to 15 - find the specific day of month input
    const dayInputs = screen.getAllByDisplayValue(/\d+/)
    const dayOfMonthInput = dayInputs.find(input =>
      input.getAttribute('max') === '31' && input.getAttribute('min') === '1'
    )!

    // Clear and set the value
    await user.clear(dayOfMonthInput)
    await user.type(dayOfMonthInput, '15')

    // Wait for the value to update
    await waitFor(() => {
      expect(dayOfMonthInput).toHaveValue(15)
    }, { timeout: 3000 })
  })

  it('should create a monthly recurrence with weekday pattern', async () => {
    const user = userEvent.setup()
    
    render(<RecurringDatePicker onChange={onChangeCallback} />)
    
    await waitFor(() => {
      expect(screen.getByText('Monthly')).toBeInTheDocument()
    })
    
    // Select monthly recurrence
    const monthlyButton = screen.getByText('Monthly')
    await user.click(monthlyButton)
    
    await waitFor(() => {
      expect(screen.getByText('Monthly Pattern')).toBeInTheDocument()
    })
    
    // Select weekday pattern
    const weekdayPatternRadio = screen.getByDisplayValue('weekday')
    await user.click(weekdayPatternRadio)
    
    expect(weekdayPatternRadio).toBeChecked()
    
    // Select "2nd Tuesday" - find the select elements by their aria-labels
    const weekSelect = screen.getByLabelText('Week of month')
    await user.selectOptions(weekSelect, '2')

    const daySelect = screen.getByLabelText('Day of week')
    await user.selectOptions(daySelect, '2') // Tuesday

    // Check that the selections were made
    expect(weekSelect).toHaveValue('2')
    expect(daySelect).toHaveValue('2')
  })

  it('should show calendar preview with recurring dates', async () => {
    const user = userEvent.setup()
    
    render(<RecurringDatePicker onChange={onChangeCallback} />)
    
    await waitFor(() => {
      expect(screen.getByText('Preview')).toBeInTheDocument()
    })
    
    // The calendar should be visible
    expect(screen.getByText('Today')).toBeInTheDocument()
    expect(screen.getByTitle('Previous month')).toBeInTheDocument()
    expect(screen.getByTitle('Next month')).toBeInTheDocument()
    
    // Should show upcoming dates
    expect(screen.getByText('Next 5 occurrences:')).toBeInTheDocument()
    
    // Navigate to next month
    const nextButton = screen.getByTitle('Next month')
    await user.click(nextButton)
    
    // Should update the month display
    // (The exact month would depend on current date)
  })

  it('should handle validation errors gracefully', async () => {
    const user = userEvent.setup()
    
    render(<RecurringDatePicker onChange={onChangeCallback} />)
    
    await waitFor(() => {
      expect(screen.getByText('Weekly')).toBeInTheDocument()
    })
    
    // Select weekly but don't select any days
    const weeklyButton = screen.getByText('Weekly')
    await user.click(weeklyButton)
    
    await waitFor(() => {
      expect(screen.getByText('On these days')).toBeInTheDocument()
    })
    
    // Clear all days
    const clearButton = screen.getByText('None')
    await user.click(clearButton)
    
    // Should show validation warning
    expect(screen.getByText('⚠️ Please select at least one day')).toBeInTheDocument()
  })
})
