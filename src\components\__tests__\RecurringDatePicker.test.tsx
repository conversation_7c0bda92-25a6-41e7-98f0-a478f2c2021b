import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { RecurringDatePicker } from '../RecurringDatePicker'
import { RecurrenceConfig } from '@/types/recurrence'

// Mock the store
jest.mock('@/store/datePickerStore', () => ({
  useDatePickerStore: () => ({
    config: {
      type: 'daily',
      interval: 1,
      startDate: new Date(2024, 0, 1),
    },
    setConfig: jest.fn(),
    updateType: jest.fn(),
    updateStartDate: jest.fn(),
  })
}))

describe('RecurringDatePicker', () => {
  const mockOnChange = jest.fn()
  
  beforeEach(() => {
    mockOnChange.mockClear()
  })

  it('should render without crashing', () => {
    render(
      <RecurringDatePicker 
        onChange={mockOnChange}
      />
    )
    
    expect(screen.getByText('Recurrence Settings')).toBeInTheDocument()
    expect(screen.getByText('Preview')).toBeInTheDocument()
  })

  it('should display recurrence type options', () => {
    render(
      <RecurringDatePicker 
        onChange={mockOnChange}
      />
    )
    
    expect(screen.getByText('Daily')).toBeInTheDocument()
    expect(screen.getByText('Weekly')).toBeInTheDocument()
    expect(screen.getByText('Monthly')).toBeInTheDocument()
    expect(screen.getByText('Yearly')).toBeInTheDocument()
  })

  it('should display interval selector', () => {
    render(
      <RecurringDatePicker 
        onChange={mockOnChange}
      />
    )
    
    expect(screen.getByText('Every')).toBeInTheDocument()
    expect(screen.getByDisplayValue('1')).toBeInTheDocument()
  })

  it('should display date range selector', () => {
    render(
      <RecurringDatePicker 
        onChange={mockOnChange}
      />
    )
    
    expect(screen.getByText('Date Range')).toBeInTheDocument()
    expect(screen.getByText('Start Date')).toBeInTheDocument()
    expect(screen.getByText('End Date (Optional)')).toBeInTheDocument()
  })

  it('should display calendar preview', () => {
    render(
      <RecurringDatePicker 
        onChange={mockOnChange}
      />
    )
    
    // Should show month navigation
    expect(screen.getByTitle('Previous month')).toBeInTheDocument()
    expect(screen.getByTitle('Next month')).toBeInTheDocument()
    expect(screen.getByText('Today')).toBeInTheDocument()
  })

  it('should handle initial value prop', () => {
    const initialConfig: RecurrenceConfig = {
      type: 'weekly',
      interval: 2,
      startDate: new Date(2024, 0, 15),
      daysOfWeek: [1, 3, 5]
    }
    
    render(
      <RecurringDatePicker 
        value={initialConfig}
        onChange={mockOnChange}
      />
    )
    
    // Component should initialize with the provided value
    // This would be tested more thoroughly with actual store integration
    expect(screen.getByText('Recurrence Settings')).toBeInTheDocument()
  })
})
