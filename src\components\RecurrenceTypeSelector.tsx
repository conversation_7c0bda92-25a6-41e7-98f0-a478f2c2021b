'use client'

import { RecurrenceType } from '@/types/recurrence'
import { useDatePickerStore } from '@/store/datePickerStore'
import { clsx } from 'clsx'

const RECURRENCE_OPTIONS: { value: RecurrenceType; label: string; icon: string }[] = [
  { value: 'daily', label: 'Daily', icon: '📅' },
  { value: 'weekly', label: 'Weekly', icon: '📆' },
  { value: 'monthly', label: 'Monthly', icon: '🗓️' },
  { value: 'yearly', label: 'Yearly', icon: '📊' },
]

export const RecurrenceTypeSelector: React.FC = () => {
  const { config, updateType } = useDatePickerStore()
  
  if (!config) return null
  
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Repeat
      </label>
      <div className="grid grid-cols-2 gap-2">
        {RECURRENCE_OPTIONS.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={() => updateType(option.value)}
            className={clsx(
              'flex items-center justify-center px-4 py-3 rounded-lg border text-sm font-medium transition-all duration-200',
              config.type === option.value
                ? 'bg-primary-50 border-primary-500 text-primary-700 ring-2 ring-primary-500'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400'
            )}
          >
            <span className="mr-2 text-lg">{option.icon}</span>
            {option.label}
          </button>
        ))}
      </div>
    </div>
  )
}
