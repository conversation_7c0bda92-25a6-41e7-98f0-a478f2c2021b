import { create } from 'zustand'
import { RecurrenceConfig, RecurrenceType, DayOfWeek, MonthlyPattern } from '@/types/recurrence'

interface DatePickerState {
  config: RecurrenceConfig | null
  isOpen: boolean
  
  // Actions
  setConfig: (config: RecurrenceConfig | null) => void
  updateType: (type: RecurrenceType) => void
  updateInterval: (interval: number) => void
  updateStartDate: (date: Date) => void
  updateEndDate: (date: Date | undefined) => void
  updateDaysOfWeek: (days: DayOfWeek[]) => void
  updateMonthlyPattern: (pattern: MonthlyPattern) => void
  updateDayOfMonth: (day: number) => void
  updateWeekOfMonth: (week: number) => void
  updateDayOfWeekInMonth: (day: DayOfWeek) => void
  setIsOpen: (open: boolean) => void
  reset: () => void
}

export const useDatePickerStore = create<DatePickerState>((set, get) => ({
  config: null,
  isOpen: false,
  
  setConfig: (config) => set({ config }),
  
  updateType: (type) => set((state) => {
    if (!state.config) return state
    
    const newConfig: RecurrenceConfig = {
      ...state.config,
      type,
      interval: 1, // Reset interval when changing type
    }
    
    // Clear type-specific properties when changing type
    if (type !== 'weekly') {
      delete newConfig.daysOfWeek
    }
    if (type !== 'monthly') {
      delete newConfig.monthlyPattern
      delete newConfig.dayOfMonth
      delete newConfig.weekOfMonth
      delete newConfig.dayOfWeekInMonth
    }
    
    return { config: newConfig }
  }),
  
  updateInterval: (interval) => set((state) => {
    if (!state.config) return state
    return {
      config: {
        ...state.config,
        interval: Math.max(1, interval)
      }
    }
  }),
  
  updateStartDate: (startDate) => set((state) => {
    if (!state.config) return state
    return {
      config: {
        ...state.config,
        startDate
      }
    }
  }),
  
  updateEndDate: (endDate) => set((state) => {
    if (!state.config) return state
    return {
      config: {
        ...state.config,
        endDate
      }
    }
  }),
  
  updateDaysOfWeek: (daysOfWeek) => set((state) => {
    if (!state.config) return state
    return {
      config: {
        ...state.config,
        daysOfWeek
      }
    }
  }),
  
  updateMonthlyPattern: (monthlyPattern) => set((state) => {
    if (!state.config) return state
    return {
      config: {
        ...state.config,
        monthlyPattern
      }
    }
  }),
  
  updateDayOfMonth: (dayOfMonth) => set((state) => {
    if (!state.config) return state
    return {
      config: {
        ...state.config,
        dayOfMonth
      }
    }
  }),
  
  updateWeekOfMonth: (weekOfMonth) => set((state) => {
    if (!state.config) return state
    return {
      config: {
        ...state.config,
        weekOfMonth
      }
    }
  }),
  
  updateDayOfWeekInMonth: (dayOfWeekInMonth) => set((state) => {
    if (!state.config) return state
    return {
      config: {
        ...state.config,
        dayOfWeekInMonth
      }
    }
  }),
  
  setIsOpen: (isOpen) => set({ isOpen }),
  
  reset: () => set({ config: null, isOpen: false })
}))
