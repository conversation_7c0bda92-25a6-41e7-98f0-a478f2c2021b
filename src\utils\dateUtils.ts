import { 
  addDays, 
  addWeeks, 
  addMonths, 
  addYears, 
  format, 
  startOfDay,
  getDay,
  getDate,
  setDate,
  lastDayOfMonth,
  isSameDay,
  isAfter,
  isBefore
} from 'date-fns'
import { RecurrenceConfig, DayOfWeek } from '@/types/recurrence'

export const formatDate = (date: Date): string => {
  return format(date, 'yyyy-MM-dd')
}

export const formatDisplayDate = (date: Date): string => {
  return format(date, 'MMM dd, yyyy')
}

export const getDayName = (dayOfWeek: DayOfWeek): string => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  return days[dayOfWeek]
}

export const getShortDayName = (dayOfWeek: DayOfWeek): string => {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
  return days[dayOfWeek]
}

export const getWeekOfMonth = (date: Date): number => {
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1)
  const dayOfMonth = getDate(date)
  const firstDayOfWeek = getDay(firstDay)
  
  return Math.ceil((dayOfMonth + firstDayOfWeek) / 7)
}

export const isLastWeekOfMonth = (date: Date): boolean => {
  const nextWeek = addDays(date, 7)
  return nextWeek.getMonth() !== date.getMonth()
}

export const getOrdinalSuffix = (num: number): string => {
  const j = num % 10
  const k = num % 100
  if (j === 1 && k !== 11) return 'st'
  if (j === 2 && k !== 12) return 'nd'
  if (j === 3 && k !== 13) return 'rd'
  return 'th'
}

// Generate recurring dates based on configuration
export const generateRecurringDates = (
  config: RecurrenceConfig,
  maxDates: number = 50
): Date[] => {
  const dates: Date[] = []
  let currentDate = startOfDay(config.startDate)
  const endDate = config.endDate ? startOfDay(config.endDate) : null

  // TODO: Remove this debug log before production
  console.log('Generating recurring dates for config:', config)
  
  let iterations = 0
  const maxIterations = maxDates * 10 // Safety limit
  
  while (dates.length < maxDates && iterations < maxIterations) {
    iterations++
    
    if (endDate && isAfter(currentDate, endDate)) {
      break
    }
    
    if (shouldIncludeDate(currentDate, config)) {
      dates.push(new Date(currentDate))
    }
    
    currentDate = getNextDate(currentDate, config)
  }
  
  return dates
}

const shouldIncludeDate = (date: Date, config: RecurrenceConfig): boolean => {
  const daysSinceStart = Math.floor(
    (date.getTime() - config.startDate.getTime()) / (1000 * 60 * 60 * 24)
  )
  
  switch (config.type) {
    case 'daily':
      return daysSinceStart >= 0 && daysSinceStart % config.interval === 0
      
    case 'weekly':
      if (config.daysOfWeek && config.daysOfWeek.length > 0) {
        const dayOfWeek = getDay(date) as DayOfWeek
        return config.daysOfWeek.includes(dayOfWeek)
      }
      return daysSinceStart >= 0 && daysSinceStart % (config.interval * 7) === 0
      
    case 'monthly':
      return isValidMonthlyDate(date, config)
      
    case 'yearly':
      const yearsSinceStart = date.getFullYear() - config.startDate.getFullYear()
      return yearsSinceStart >= 0 && yearsSinceStart % config.interval === 0 &&
             date.getMonth() === config.startDate.getMonth() &&
             getDate(date) === getDate(config.startDate)
      
    default:
      return false
  }
}

const isValidMonthlyDate = (date: Date, config: RecurrenceConfig): boolean => {
  const monthsSinceStart = 
    (date.getFullYear() - config.startDate.getFullYear()) * 12 +
    (date.getMonth() - config.startDate.getMonth())
  
  if (monthsSinceStart < 0 || monthsSinceStart % config.interval !== 0) {
    return false
  }
  
  if (config.monthlyPattern === 'date') {
    const targetDay = config.dayOfMonth || getDate(config.startDate)
    const lastDay = getDate(lastDayOfMonth(date))
    const actualDay = Math.min(targetDay, lastDay)
    return getDate(date) === actualDay
  }
  
  if (config.monthlyPattern === 'weekday') {
    const targetDayOfWeek = config.dayOfWeekInMonth ?? getDay(config.startDate) as DayOfWeek
    const targetWeek = config.weekOfMonth ?? getWeekOfMonth(config.startDate)
    
    const dayOfWeek = getDay(date) as DayOfWeek
    if (dayOfWeek !== targetDayOfWeek) return false
    
    if (targetWeek === -1) {
      return isLastWeekOfMonth(date)
    } else {
      return getWeekOfMonth(date) === targetWeek
    }
  }
  
  return false
}

const getNextDate = (currentDate: Date, config: RecurrenceConfig): Date => {
  switch (config.type) {
    case 'daily':
      return addDays(currentDate, 1)
    case 'weekly':
      return addDays(currentDate, 1)
    case 'monthly':
      return addDays(currentDate, 1)
    case 'yearly':
      return addDays(currentDate, 1)
    default:
      return addDays(currentDate, 1)
  }
}
