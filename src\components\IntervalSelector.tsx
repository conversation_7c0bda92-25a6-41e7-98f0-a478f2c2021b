'use client'

import { useDatePickerStore } from '@/store/datePickerStore'

export const IntervalSelector: React.FC = () => {
  const { config, updateInterval } = useDatePickerStore()
  
  if (!config) return null
  
  const getIntervalLabel = () => {
    switch (config.type) {
      case 'daily':
        return config.interval === 1 ? 'day' : 'days'
      case 'weekly':
        return config.interval === 1 ? 'week' : 'weeks'
      case 'monthly':
        return config.interval === 1 ? 'month' : 'months'
      case 'yearly':
        return config.interval === 1 ? 'year' : 'years'
      default:
        return 'interval'
    }
  }
  
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Every
      </label>
      <div className="flex items-center space-x-2">
        <input
          type="number"
          min="1"
          max="999"
          value={config.interval}
          onChange={(e) => updateInterval(parseInt(e.target.value) || 1)}
          className="input-field w-20 text-center"
        />
        <span className="text-sm text-gray-600">
          {getIntervalLabel()}
        </span>
      </div>
    </div>
  )
}
