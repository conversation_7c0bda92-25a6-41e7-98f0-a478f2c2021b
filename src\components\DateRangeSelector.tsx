'use client'

import { useDatePickerStore } from '@/store/datePickerStore'
import { formatDate } from '@/utils/dateUtils'

interface DateRangeSelectorProps {
  minDate?: Date
  maxDate?: Date
}

export const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  minDate,
  maxDate
}) => {
  const { config, updateStartDate, updateEndDate } = useDatePickerStore()
  
  if (!config) return null
  
  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const date = new Date(e.target.value)
    if (!isNaN(date.getTime())) {
      updateStartDate(date)
    }
  }
  
  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (value === '') {
      updateEndDate(undefined)
    } else {
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        updateEndDate(date)
      }
    }
  }
  
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Start Date
        </label>
        <input
          type="date"
          value={formatDate(config.startDate)}
          onChange={handleStartDateChange}
          min={minDate ? formatDate(minDate) : undefined}
          max={maxDate ? formatDate(maxDate) : undefined}
          className="input-field w-full"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          End Date (Optional)
        </label>
        <input
          type="date"
          value={config.endDate ? formatDate(config.endDate) : ''}
          onChange={handleEndDateChange}
          min={formatDate(config.startDate)}
          max={maxDate ? formatDate(maxDate) : undefined}
          className="input-field w-full"
        />
        <p className="text-xs text-gray-500 mt-1">
          Leave empty for no end date
        </p>
      </div>
    </div>
  )
}
