export type RecurrenceType = 'daily' | 'weekly' | 'monthly' | 'yearly'

export type DayOfWeek = 0 | 1 | 2 | 3 | 4 | 5 | 6 // Sunday = 0, Monday = 1, etc.

export type MonthlyPattern = 'date' | 'weekday' // e.g., "15th of month" vs "2nd Tuesday"

export interface RecurrenceConfig {
  type: RecurrenceType
  interval: number // Every X days/weeks/months/years
  startDate: Date
  endDate?: Date
  
  // Weekly specific
  daysOfWeek?: DayOfWeek[]
  
  // Monthly specific
  monthlyPattern?: MonthlyPattern
  dayOfMonth?: number // For 'date' pattern
  weekOfMonth?: number // For 'weekday' pattern (1-4, or -1 for last)
  dayOfWeekInMonth?: DayOfWeek // For 'weekday' pattern
}

export interface DatePickerProps {
  value?: RecurrenceConfig | null
  onChange: (config: RecurrenceConfig | null) => void
  minDate?: Date
  maxDate?: Date
  className?: string
}
