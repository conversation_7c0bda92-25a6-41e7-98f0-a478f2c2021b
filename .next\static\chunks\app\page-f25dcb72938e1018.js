(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2244:(e,t,a)=>{Promise.resolve().then(a.bind(a,6456))},6456:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>F});var s=a(5155),l=a(2115);let n=(0,a(8693).vt)((e,t)=>({config:null,isOpen:!1,setConfig:t=>e({config:t}),updateType:t=>e(e=>{if(!e.config)return e;let a={...e.config,type:t,interval:1};return"weekly"!==t&&delete a.daysOfWeek,"monthly"!==t&&(delete a.monthlyPattern,delete a.dayOfMonth,delete a.weekOfMonth,delete a.dayOfWeekInMonth),{config:a}}),updateInterval:t=>e(e=>e.config?{config:{...e.config,interval:Math.max(1,t)}}:e),updateStartDate:t=>e(e=>e.config?{config:{...e.config,startDate:t}}:e),updateEndDate:t=>e(e=>e.config?{config:{...e.config,endDate:t}}:e),updateDaysOfWeek:t=>e(e=>e.config?{config:{...e.config,daysOfWeek:t}}:e),updateMonthlyPattern:t=>e(e=>e.config?{config:{...e.config,monthlyPattern:t}}:e),updateDayOfMonth:t=>e(e=>e.config?{config:{...e.config,dayOfMonth:t}}:e),updateWeekOfMonth:t=>e(e=>e.config?{config:{...e.config,weekOfMonth:t}}:e),updateDayOfWeekInMonth:t=>e(e=>e.config?{config:{...e.config,dayOfWeekInMonth:t}}:e),setIsOpen:t=>e({isOpen:t}),reset:()=>e({config:null,isOpen:!1})}));var r=a(2596);let i=[{value:"daily",label:"Daily",icon:"\uD83D\uDCC5"},{value:"weekly",label:"Weekly",icon:"\uD83D\uDCC6"},{value:"monthly",label:"Monthly",icon:"\uD83D\uDDD3️"},{value:"yearly",label:"Yearly",icon:"\uD83D\uDCCA"}],d=()=>{let{config:e,updateType:t}=n();return e?(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Repeat"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2",children:i.map(a=>(0,s.jsxs)("button",{type:"button",onClick:()=>t(a.value),className:(0,r.$)("flex items-center justify-center px-4 py-3 rounded-lg border text-sm font-medium transition-all duration-200",e.type===a.value?"bg-primary-50 border-primary-500 text-primary-700 ring-2 ring-primary-500":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"),children:[(0,s.jsx)("span",{className:"mr-2 text-lg",children:a.icon}),a.label]},a.value))})]}):null},c=()=>{let{config:e,updateInterval:t}=n();return e?(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Every"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"number",min:"1",max:"999",value:e.interval,onChange:e=>t(parseInt(e.target.value)||1),className:"input-field w-20 text-center"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:(()=>{switch(e.type){case"daily":return 1===e.interval?"day":"days";case"weekly":return 1===e.interval?"week":"weeks";case"monthly":return 1===e.interval?"month":"months";case"yearly":return 1===e.interval?"year":"years";default:return"interval"}})()})]})]}):null};var o=a(4434),x=a(1851),m=a(1625),g=a(3127),u=a(3566),y=a(5833),h=a(9448);let p=e=>(0,o.A)(e,"yyyy-MM-dd"),f=e=>{let t=new Date(e.getFullYear(),e.getMonth(),1);return Math.ceil(((0,x.A)(e)+(0,m.A)(t))/7)},v=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,a=[],s=(0,u.A)(e.startDate),l=e.endDate?(0,u.A)(e.endDate):null;console.log("Generating recurring dates for config:",e);let n=0,r=10*t;for(;a.length<t&&n<r&&(n++,!(l&&(0,y.A)(s,l)));)b(s,e)&&a.push(new Date(s)),s=N(s,e);return a},b=(e,t)=>{let a=Math.floor((e.getTime()-t.startDate.getTime())/864e5);switch(t.type){case"daily":return a>=0&&a%t.interval==0;case"weekly":if(t.daysOfWeek&&t.daysOfWeek.length>0){let a=(0,m.A)(e);return t.daysOfWeek.includes(a)}return a>=0&&a%(7*t.interval)==0;case"monthly":return j(e,t);case"yearly":let s=e.getFullYear()-t.startDate.getFullYear();return s>=0&&s%t.interval==0&&e.getMonth()===t.startDate.getMonth()&&(0,x.A)(e)===(0,x.A)(t.startDate);default:return!1}},j=(e,t)=>{let a=(e.getFullYear()-t.startDate.getFullYear())*12+(e.getMonth()-t.startDate.getMonth());if(a<0||a%t.interval!=0)return!1;if("date"===t.monthlyPattern){let a=Math.min(t.dayOfMonth||(0,x.A)(t.startDate),(0,x.A)((0,h.A)(e)));return(0,x.A)(e)===a}if("weekday"===t.monthlyPattern){var s,l;let a=null!=(s=t.dayOfWeekInMonth)?s:(0,m.A)(t.startDate),n=null!=(l=t.weekOfMonth)?l:f(t.startDate);return(0,m.A)(e)===a&&(-1===n?(e=>(0,g.A)(e,7).getMonth()!==e.getMonth())(e):f(e)===n)}return!1},N=(e,t)=>(t.type,(0,g.A)(e,1)),k=e=>{let{minDate:t,maxDate:a}=e,{config:l,updateStartDate:r,updateEndDate:i}=n();return l?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),(0,s.jsx)("input",{type:"date",value:p(l.startDate),onChange:e=>{let t=new Date(e.target.value);isNaN(t.getTime())||r(t)},min:t?p(t):void 0,max:a?p(a):void 0,className:"input-field w-full"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date (Optional)"}),(0,s.jsx)("input",{type:"date",value:l.endDate?p(l.endDate):"",onChange:e=>{let t=e.target.value;if(""===t)i(void 0);else{let e=new Date(t);isNaN(e.getTime())||i(e)}},min:p(l.startDate),max:a?p(a):void 0,className:"input-field w-full"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Leave empty for no end date"})]})]}):null},w=[0,1,2,3,4,5,6],D=()=>{let{config:e,updateDaysOfWeek:t}=n();if(!e||"weekly"!==e.type)return null;let a=e.daysOfWeek||[];return(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"On these days"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-3",children:[(0,s.jsx)("button",{type:"button",onClick:()=>{t([1,2,3,4,5])},className:"text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 transition-colors",children:"Weekdays"}),(0,s.jsx)("button",{type:"button",onClick:()=>{t([0,6])},className:"text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 transition-colors",children:"Weekends"}),(0,s.jsx)("button",{type:"button",onClick:()=>{t([...w])},className:"text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 transition-colors",children:"All"}),(0,s.jsx)("button",{type:"button",onClick:()=>{t([])},className:"text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 transition-colors",children:"None"})]}),(0,s.jsx)("div",{className:"grid grid-cols-7 gap-1",children:w.map(e=>(0,s.jsx)("button",{type:"button",onClick:()=>(e=>{t(a.includes(e)?a.filter(t=>t!==e):[...a,e].sort((e,t)=>e-t))})(e),className:(0,r.$)("aspect-square flex items-center justify-center text-xs font-medium rounded transition-all duration-200",a.includes(e)?"bg-primary-500 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"][e]},e))}),0===a.length&&(0,s.jsx)("p",{className:"text-xs text-amber-600 mt-2",children:"⚠️ Please select at least one day"})]})},M=()=>{let{config:e,updateMonthlyPattern:t,updateDayOfMonth:a,updateWeekOfMonth:l,updateDayOfWeekInMonth:i}=n();if(!e||"monthly"!==e.type)return null;let d=e.monthlyPattern||"date",c=(0,m.A)(e.startDate),o=(0,x.A)(e.startDate),g=f(e.startDate),u=e=>{t(e),"date"===e?a(o):"weekday"===e&&(l(g),i(c))};return(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Monthly Pattern"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"flex items-start space-x-3",children:[(0,s.jsx)("input",{type:"radio",name:"monthlyPattern",value:"date",checked:"date"===d,onChange:()=>u("date"),className:"mt-1"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"On day"}),(0,s.jsx)("input",{type:"number",min:"1",max:"31",value:e.dayOfMonth||o,onChange:e=>{let t=parseInt(e.target.value);t>=1&&t<=31&&a(t)},disabled:"date"!==d,className:(0,r.$)("input-field w-16 text-center","date"!==d&&"bg-gray-100 text-gray-500")}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"of each month"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:'e.g., "15th of every month"'})]})]}),(0,s.jsxs)("label",{className:"flex items-start space-x-3",children:[(0,s.jsx)("input",{type:"radio",name:"monthlyPattern",value:"weekday",checked:"weekday"===d,onChange:()=>u("weekday"),className:"mt-1"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-wrap",children:[(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"On the"}),(0,s.jsxs)("select",{value:e.weekOfMonth||g,onChange:e=>{l(parseInt(e.target.value))},disabled:"weekday"!==d,className:(0,r.$)("input-field text-sm","weekday"!==d&&"bg-gray-100 text-gray-500"),children:[(0,s.jsx)("option",{value:1,children:"1st"}),(0,s.jsx)("option",{value:2,children:"2nd"}),(0,s.jsx)("option",{value:3,children:"3rd"}),(0,s.jsx)("option",{value:4,children:"4th"}),(0,s.jsx)("option",{value:-1,children:"last"})]}),(0,s.jsx)("select",{value:e.dayOfWeekInMonth||c,onChange:e=>{i(parseInt(e.target.value))},disabled:"weekday"!==d,className:(0,r.$)("input-field text-sm","weekday"!==d&&"bg-gray-100 text-gray-500"),children:[0,1,2,3,4,5,6].map(e=>(0,s.jsx)("option",{value:e,children:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][e]},e))}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"of each month"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:'e.g., "2nd Tuesday of every month"'})]})]})]})]})})};var A=a(3054),O=a(8569),C=a(4994),W=a(787),S=a(7359),P=a(3318),T=a(5767);let I=e=>{let{config:t}=e,[a,n]=(0,l.useState)(new Date),i=(0,l.useMemo)(()=>v(t,100),[t]),d=(0,l.useMemo)(()=>{let e=(0,A.A)(a),t=(0,O.A)(a);return(0,C.A)({start:e,end:t})},[a]),c=(0,l.useMemo)(()=>{let e=(0,A.A)(a),t=(0,m.A)(e),s=[];for(let a=t-1;a>=0;a--){let t=new Date(e);t.setDate(t.getDate()-(a+1)),s.push(t)}let l=(0,O.A)(a),n=42-d.length-s.length,r=[];for(let e=1;e<=n;e++){let t=new Date(l);t.setDate(t.getDate()+e),r.push(t)}return[...s,...d,...r]},[a,d]),x=i.filter(e=>e>=new Date).slice(0,5);return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:(0,o.A)(a,"MMMM yyyy")}),(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("button",{onClick:()=>n((0,P.A)(a,1)),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",title:"Previous month",children:"←"}),(0,s.jsx)("button",{onClick:()=>n(new Date),className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:"Today"}),(0,s.jsx)("button",{onClick:()=>n((0,S.A)(a,1)),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",title:"Next month",children:"→"})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,s.jsx)("div",{className:"grid grid-cols-7 bg-gray-50",children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,s.jsx)("div",{className:"p-2 text-center text-xs font-medium text-gray-500",children:e},e))}),(0,s.jsx)("div",{className:"grid grid-cols-7",children:c.map((e,t)=>{let l=(0,T.A)(e,a),n=i.some(t=>(0,W.A)(e,t)),d=(0,W.A)(e,new Date);return(0,s.jsxs)("div",{className:(0,r.$)("aspect-square flex items-center justify-center text-sm relative border-r border-b border-gray-100",!l&&"text-gray-300",l&&"text-gray-900",d&&"bg-blue-50",n&&l&&"bg-primary-100"),children:[(0,s.jsx)("span",{className:(0,r.$)("relative z-10",n&&l&&"font-semibold text-primary-700"),children:(0,o.A)(e,"d")}),n&&l&&(0,s.jsx)("div",{className:"absolute inset-0 bg-primary-500 opacity-20 rounded-full m-1"}),d&&(0,s.jsx)("div",{className:"absolute inset-0 border-2 border-blue-500 rounded-full m-1"})]},t)})})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Next 5 occurrences:"}),x.length>0?(0,s.jsx)("ul",{className:"space-y-1",children:x.map((e,t)=>(0,s.jsx)("li",{className:"text-sm text-gray-600",children:(0,o.A)(e,"MMM dd, yyyy")},t))}):(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"No upcoming dates"})]})]})},E=e=>{let{value:t,onChange:a,minDate:i,maxDate:o,className:x}=e,{config:m,setConfig:g,updateType:u,updateStartDate:y}=n();return((0,l.useEffect)(()=>{t?g(t):m||g({type:"daily",interval:1,startDate:new Date})},[t,m,g]),(0,l.useEffect)(()=>{m&&a&&a(m)},[m,a]),m)?(0,s.jsx)("div",{className:(0,r.$)("space-y-6",x),children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Recurrence Settings"}),(0,s.jsx)(d,{}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(c,{})}),"weekly"===m.type&&(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(D,{})}),"monthly"===m.type&&(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(M,{})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Date Range"}),(0,s.jsx)(k,{minDate:i,maxDate:o})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Preview"}),(0,s.jsx)(I,{config:m})]})]})}):(0,s.jsx)("div",{children:"Loading..."})};function F(){let[e,t]=(0,l.useState)(null);return(0,s.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Recurring Date Picker Demo"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Select recurring dates with customizable patterns"})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:(0,s.jsx)(E,{onChange:t,value:e})}),e&&(0,s.jsxs)("div",{className:"mt-8 bg-white rounded-lg shadow-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Selected Configuration"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded-lg overflow-auto",children:JSON.stringify(e,null,2)})]})]})})}}},e=>{e.O(0,[625,441,964,358],()=>e(e.s=2244)),_N_E=e.O()}]);