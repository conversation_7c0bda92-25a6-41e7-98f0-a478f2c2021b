[{"C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\CalendarPreview.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\DateRangeSelector.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\IntervalSelector.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\MonthlyOptions.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\RecurrenceTypeSelector.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\RecurringDatePicker.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\WeeklyOptions.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\__tests__\\RecurringDatePicker.test.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\store\\datePickerStore.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\types\\recurrence.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\utils\\dateUtils.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\utils\\__tests__\\dateUtils.test.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\__tests__\\integration\\RecurringDatePicker.integration.test.tsx": "15"}, {"size": 486, "mtime": 1753036790581, "results": "16", "hashOfConfig": "17"}, {"size": 1231, "mtime": 1753036798494, "results": "18", "hashOfConfig": "17"}, {"size": 5690, "mtime": 1753037127478, "results": "19", "hashOfConfig": "17"}, {"size": 1938, "mtime": 1753036898919, "results": "20", "hashOfConfig": "17"}, {"size": 1187, "mtime": 1753036888723, "results": "21", "hashOfConfig": "17"}, {"size": 5290, "mtime": 1753036940210, "results": "22", "hashOfConfig": "17"}, {"size": 1521, "mtime": 1753036880965, "results": "23", "hashOfConfig": "17"}, {"size": 2695, "mtime": 1753036866881, "results": "24", "hashOfConfig": "17"}, {"size": 3027, "mtime": 1753036919097, "results": "25", "hashOfConfig": "17"}, {"size": 2868, "mtime": 1753037015087, "results": "26", "hashOfConfig": "17"}, {"size": 3122, "mtime": 1753036845306, "results": "27", "hashOfConfig": "17"}, {"size": 857, "mtime": 1753036807608, "results": "28", "hashOfConfig": "17"}, {"size": 4753, "mtime": 1753037111714, "results": "29", "hashOfConfig": "17"}, {"size": 4719, "mtime": 1753037001051, "results": "30", "hashOfConfig": "17"}, {"size": 7392, "mtime": 1753037052542, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mw4vrq", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\CalendarPreview.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\DateRangeSelector.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\IntervalSelector.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\MonthlyOptions.tsx", ["77", "78", "79", "80"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\RecurrenceTypeSelector.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\RecurringDatePicker.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\WeeklyOptions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\components\\__tests__\\RecurringDatePicker.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\store\\datePickerStore.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\types\\recurrence.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\utils\\dateUtils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\utils\\__tests__\\dateUtils.test.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Recurring Date Picker Component\\src\\__tests__\\integration\\RecurringDatePicker.integration.test.tsx", [], [], {"ruleId": "81", "severity": 2, "message": "82", "line": 89, "column": 23, "nodeType": "83", "messageId": "84", "suggestions": "85"}, {"ruleId": "81", "severity": 2, "message": "82", "line": 89, "column": 43, "nodeType": "83", "messageId": "84", "suggestions": "86"}, {"ruleId": "81", "severity": 2, "message": "82", "line": 140, "column": 23, "nodeType": "83", "messageId": "84", "suggestions": "87"}, {"ruleId": "81", "severity": 2, "message": "82", "line": 140, "column": 50, "nodeType": "83", "messageId": "84", "suggestions": "88"}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["89", "90", "91", "92"], ["93", "94", "95", "96"], ["97", "98", "99", "100"], ["101", "102", "103", "104"], {"messageId": "105", "data": "106", "fix": "107", "desc": "108"}, {"messageId": "105", "data": "109", "fix": "110", "desc": "111"}, {"messageId": "105", "data": "112", "fix": "113", "desc": "114"}, {"messageId": "105", "data": "115", "fix": "116", "desc": "117"}, {"messageId": "105", "data": "118", "fix": "119", "desc": "108"}, {"messageId": "105", "data": "120", "fix": "121", "desc": "111"}, {"messageId": "105", "data": "122", "fix": "123", "desc": "114"}, {"messageId": "105", "data": "124", "fix": "125", "desc": "117"}, {"messageId": "105", "data": "126", "fix": "127", "desc": "108"}, {"messageId": "105", "data": "128", "fix": "129", "desc": "111"}, {"messageId": "105", "data": "130", "fix": "131", "desc": "114"}, {"messageId": "105", "data": "132", "fix": "133", "desc": "117"}, {"messageId": "105", "data": "134", "fix": "135", "desc": "108"}, {"messageId": "105", "data": "136", "fix": "137", "desc": "111"}, {"messageId": "105", "data": "138", "fix": "139", "desc": "114"}, {"messageId": "105", "data": "140", "fix": "141", "desc": "117"}, "replaceWithAlt", {"alt": "142"}, {"range": "143", "text": "144"}, "Replace with `&quot;`.", {"alt": "145"}, {"range": "146", "text": "147"}, "Replace with `&ldquo;`.", {"alt": "148"}, {"range": "149", "text": "150"}, "Replace with `&#34;`.", {"alt": "151"}, {"range": "152", "text": "153"}, "Replace with `&rdquo;`.", {"alt": "142"}, {"range": "154", "text": "155"}, {"alt": "145"}, {"range": "156", "text": "157"}, {"alt": "148"}, {"range": "158", "text": "159"}, {"alt": "151"}, {"range": "160", "text": "161"}, {"alt": "142"}, {"range": "162", "text": "163"}, {"alt": "145"}, {"range": "164", "text": "165"}, {"alt": "148"}, {"range": "166", "text": "167"}, {"alt": "151"}, {"range": "168", "text": "169"}, {"alt": "142"}, {"range": "170", "text": "171"}, {"alt": "145"}, {"range": "172", "text": "173"}, {"alt": "148"}, {"range": "174", "text": "175"}, {"alt": "151"}, {"range": "176", "text": "177"}, "&quot;", [3008, 3067], "\n                e.g., &quot;15th of every month\"\n              ", "&ldquo;", [3008, 3067], "\n                e.g., &ldquo;15th of every month\"\n              ", "&#34;", [3008, 3067], "\n                e.g., &#34;15th of every month\"\n              ", "&rdquo;", [3008, 3067], "\n                e.g., &rdquo;15th of every month\"\n              ", [3008, 3067], "\n                e.g., \"15th of every month&quot;\n              ", [3008, 3067], "\n                e.g., \"15th of every month&ldquo;\n              ", [3008, 3067], "\n                e.g., \"15th of every month&#34;\n              ", [3008, 3067], "\n                e.g., \"15th of every month&rdquo;\n              ", [5136, 5202], "\n                e.g., &quot;2nd Tuesday of every month\"\n              ", [5136, 5202], "\n                e.g., &ldquo;2nd Tuesday of every month\"\n              ", [5136, 5202], "\n                e.g., &#34;2nd Tuesday of every month\"\n              ", [5136, 5202], "\n                e.g., &rdquo;2nd Tuesday of every month\"\n              ", [5136, 5202], "\n                e.g., \"2nd Tuesday of every month&quot;\n              ", [5136, 5202], "\n                e.g., \"2nd Tuesday of every month&ldquo;\n              ", [5136, 5202], "\n                e.g., \"2nd Tuesday of every month&#34;\n              ", [5136, 5202], "\n                e.g., \"2nd Tuesday of every month&rdquo;\n              "]